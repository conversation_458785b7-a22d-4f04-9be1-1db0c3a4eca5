"""Agent package - Email processing agent using planned_workflow."""

from typing import Dict, Any
from planned_workflow.config import WorkflowConfig
from planned_workflow.graph import create_planned_workflow, run_planned_workflow
# Import tools directly to avoid circular imports
from agent.tools.email_tool import create_email
from agent.tools.attachment_tools import get_file_attachment, list_email_attachments
from agent.tools.file_processing_tool import process_file_with_llm
from agent.tools.accounting_tool import accounting
from agent.prompts.analyze_messages import get_analyze_messages_system_prompt


def create_agent_config() -> WorkflowConfig:
    """
    Create the workflow configuration for the email processing agent.

    Returns:
        WorkflowConfig: Configured workflow for the agent
    """
    # Get the agent's system prompt extension from the existing analyze_messages prompt
    system_prompt = get_analyze_messages_system_prompt()

    # Extract the core system prompt content (remove the output format part)
    # We'll use the planned_workflow's own output format handling
    system_lines = system_prompt.split('\n')

    # Find where the output format section starts and exclude it
    system_content_lines = []
    skip_section = False

    for line in system_lines:
        if '### Output Format' in line:
            skip_section = True
            continue
        if not skip_section:
            system_content_lines.append(line)

    system_prompt_extension = '\n'.join(system_content_lines)

    return WorkflowConfig(
        workflow_name="email_processing_agent",
        description="AI agent for processing email instructions and automating tasks",
        tools=[create_email, get_file_attachment, list_email_attachments, process_file_with_llm, accounting],
        system_prompt_extension=system_prompt_extension,
        max_tasks=5,
        max_steps=3,
        metadata={
            "agent_type": "email_processor",
            "version": "2.0"
        }
    )


def create_agent_graph():
    """
    Create and configure the email processing agent graph.

    Returns:
        Compiled LangGraph agent using planned_workflow
    """
    config = create_agent_config()
    return create_planned_workflow(config)


def run_agent(input_text: str, sender_email: str, thread_id: str = "default") -> Dict[str, Any]:
    """
    Run the email processing agent with the given input.

    Args:
        input_text: Text containing instructions to process
        sender_email: Email address of the sender (stored in metadata)
        thread_id: Thread ID for conversation tracking

    Returns:
        Agent response with processed instructions and metadata
    """
    config = create_agent_config()

    try:
        # Run the planned workflow
        result = run_planned_workflow(config, input_text, thread_id)

        # Transform the result to match the expected agent output format
        return {
            "success": not bool(result.get("error")),
            "output": result.get("output", "No output generated"),
            "pending_emails": result.get("pending_emails", []),
            "metadata": {
                "model_used": result.get("model_used"),
                "processing_time": result.get("processing_time"),
                "timestamp": result.get("timestamp"),
                "thread_id": thread_id,
                "sender_email": sender_email,
                "steps_taken": result.get("current_step", 0),
                "workflow_name": config.workflow_name
            },
            "error": result.get("error")
        }

    except Exception as e:
        return {
            "success": False,
            "output": None,
            "metadata": {
                "thread_id": thread_id,
                "sender_email": sender_email,
                "workflow_name": config.workflow_name
            },
            "error": f"Agent execution failed: {str(e)}"
        }


# For backward compatibility, keep the old interface available
from agent.graph import create_agent_graph as _legacy_create_agent_graph
from agent.graph import run_agent as _legacy_run_agent

# Export both new and legacy interfaces
__all__ = [
    'create_agent_config',
    'create_agent_graph',
    'run_agent',
    '_legacy_create_agent_graph',
    '_legacy_run_agent'
]
