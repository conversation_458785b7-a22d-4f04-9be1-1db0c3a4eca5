"""Generic planning node for planned workflows."""

import time
import logging
from typing import Dict, Any, Callable
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langchain.schema import HumanMessage, SystemMessage

from agent.state import ExecutionPlan
from ..state import PlannedWorkflowState
from ..config import WorkflowConfig

logger = logging.getLogger(__name__)


def create_planning_node(config: WorkflowConfig) -> Callable[[PlannedWorkflowState], Dict[str, Any]]:
    """
    Create a planning node configured for a specific workflow.
    
    Args:
        config: Workflow configuration
        
    Returns:
        Planning node function
    """
    
    def planning_node(state: PlannedWorkflowState) -> Dict[str, Any]:
        """
        Planning node that creates an execution plan from the input.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with execution plan
        """
        start_time = time.time()
        
        try:
            # Get input text
            input_text = state.get("input", "")
            
            # Initialize the OpenAI model for planning
            llm = ChatOpenAI(
                model=config.models.planning_model,
                temperature=config.models.temperature,
                max_tokens=config.models.max_tokens,
                openai_api_key=config.models.openai_api_key
            )
            
            # Build system prompt
            base_system_prompt = f"""You are an expert planning assistant for {config.workflow_name}.

Create a detailed execution plan for the given request. Break down the work into specific, actionable tasks.

Available tools and capabilities:
{_get_tools_description(config)}

Maximum number of tasks: {config.max_tasks}

Guidelines:
- Each task should be specific and actionable
- Use imperative verbs (e.g., "Create", "Send", "Analyze")
- Specify expected deliverables clearly
- If information is unclear, add clarification questions
- Tasks should be executable with the available tools

{config.system_prompt_extension}

Analyze the request and create a structured plan with concrete tasks."""
            
            # Create messages
            system_message = SystemMessage(content=base_system_prompt)
            human_message = HumanMessage(content=input_text)
            
            # Get plan from OpenAI with structured output
            response = llm.with_structured_output(ExecutionPlan).invoke([system_message, human_message])
            
            processing_time = time.time() - start_time
            
            logger.info(f"Created {config.workflow_name} plan with {len(response.tasks)} tasks: {response.plan_summary}")
            
            return {
                "plan": response,
                "processing_time": processing_time,
                "model_used": config.models.planning_model,
                "messages": [system_message, human_message]
            }
            
        except Exception as e:
            logger.error(f"Error in planning node: {e}")
            return {
                "error": f"Planning failed: {str(e)}",
                "processing_time": time.time() - start_time
            }
    
    return planning_node


def _get_tools_description(config: WorkflowConfig) -> str:
    """Generate description of available tools."""
    if not config.tools:
        return "No specific tools configured."
    
    descriptions = []
    for tool in config.tools:
        descriptions.append(f"- {tool.name}: {tool.description}")
    
    if config.subgraph:
        descriptions.append(f"- Subgraph: {config.description}")
    
    return "\n".join(descriptions)
