"""Generic tool results processing node for planned workflows."""

import json
import time
import logging
from typing import Dict, Any, Callable, List
from langchain.schema import BaseMessage

from ..state import PlannedWorkflowState
from ..config import WorkflowConfig
from agent.state import EmailMessage

logger = logging.getLogger(__name__)


def extract_emails_from_messages(messages: List[BaseMessage]) -> List[EmailMessage]:
    """
    Extract EmailMessage objects from tool call responses in messages.

    Args:
        messages: List of messages that may contain tool call responses

    Returns:
        List of EmailMessage objects found in the messages
    """
    emails = []

    for message in messages:
        # Check if this is an AI message with tool calls
        if hasattr(message, 'tool_calls') and message.tool_calls:
            for tool_call in message.tool_calls:
                # Check if this is a create_email tool call
                if tool_call.get('name') == 'create_email':
                    try:
                        # The tool returns a string starting with "EMAIL_CREATED:"
                        # We need to look at the actual tool response, not just the call
                        pass
                    except Exception as e:
                        logger.error(f"Error extracting email from tool call: {e}")

        # Check message content for EMAIL_CREATED responses
        if hasattr(message, 'content') and message.content:
            content = str(message.content)
            if "EMAIL_CREATED:" in content:
                try:
                    # Extract JSON part after EMAIL_CREATED:
                    json_start = content.find("EMAIL_CREATED:") + len("EMAIL_CREATED:")
                    json_str = content[json_start:].strip()

                    # Find the end of the JSON (look for the closing brace)
                    brace_count = 0
                    json_end = 0
                    for i, char in enumerate(json_str):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                json_end = i + 1
                                break

                    if json_end > 0:
                        json_str = json_str[:json_end]
                        email_data = json.loads(json_str)
                        email = EmailMessage(**email_data)
                        emails.append(email)

                except Exception as e:
                    logger.error(f"Error parsing email from message content: {e}")

    return emails


def create_tool_results_node(config: WorkflowConfig) -> Callable[[PlannedWorkflowState], Dict[str, Any]]:
    """
    Create a tool results processing node configured for a specific workflow.
    
    Args:
        config: Workflow configuration
        
    Returns:
        Tool results processing node function
    """
    
    def process_tool_results_node(state: PlannedWorkflowState) -> Dict[str, Any]:
        """
        Process tool results and determine next steps.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with processing results
        """
        start_time = time.time()
        
        try:
            # Get current plan, step, and messages
            plan = state.get("plan")
            current_step = state.get("current_step", 0)
            max_steps = state.get("max_steps", config.max_steps)
            messages = state.get("messages", [])
            current_pending_emails = state.get("pending_emails", [])

            if not plan or not plan.tasks:
                return {
                    "error": "No plan available for processing",
                    "processing_time": time.time() - start_time
                }

            # Extract emails from tool responses
            new_emails = extract_emails_from_messages(messages)
            all_pending_emails = current_pending_emails + new_emails

            logger.info(f"Extracted {len(new_emails)} emails from tool responses")
            
            # Check if we've exceeded max steps
            if current_step >= max_steps:
                logger.warning(f"Maximum steps ({max_steps}) reached in {config.workflow_name}")
                return {
                    "analysis": f"Maximum execution steps ({max_steps}) reached. Stopping execution.",
                    "needs_action": False,
                    "processing_time": time.time() - start_time
                }
            
            # Get current task
            current_task_index = plan.current_task_index
            if current_task_index >= len(plan.tasks):
                return {
                    "analysis": "All tasks completed successfully",
                    "needs_action": False,
                    "processing_time": time.time() - start_time
                }
            
            current_task = plan.tasks[current_task_index]
            
            # Mark current task as completed and move to next
            current_task.completed = True
            plan.current_task_index += 1
            
            # Increment step counter
            current_step += 1
            
            # Check if there are more tasks
            if plan.current_task_index < len(plan.tasks):
                next_task = plan.tasks[plan.current_task_index]
                logger.info(f"Task {current_task.id} completed, moving to {next_task.id}")
                
                return {
                    "plan": plan,
                    "current_step": current_step,
                    "pending_emails": all_pending_emails,
                    "analysis": f"Task {current_task.id} completed. Next: {next_task.id}",
                    "needs_action": True,
                    "processing_time": time.time() - start_time
                }
            else:
                logger.info(f"All tasks completed in {config.workflow_name}")
                return {
                    "plan": plan,
                    "current_step": current_step,
                    "pending_emails": all_pending_emails,
                    "analysis": "All tasks completed successfully",
                    "needs_action": False,
                    "processing_time": time.time() - start_time
                }
                
        except Exception as e:
            logger.error(f"Error in tool results processing: {e}")
            return {
                "error": f"Tool results processing failed: {str(e)}",
                "processing_time": time.time() - start_time
            }
    
    return process_tool_results_node
