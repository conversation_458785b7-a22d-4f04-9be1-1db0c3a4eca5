"""Generic task execution node for planned workflows."""

import time
import logging
from typing import Dict, Any, Callable
from langchain_openai import Chat<PERSON>penAI
from langchain.schema import HumanMessage, SystemMessage

from ..state import PlannedWorkflowState
from ..config import WorkflowConfig

logger = logging.getLogger(__name__)


def create_task_node(config: WorkflowConfig) -> Callable[[PlannedWorkflowState], Dict[str, Any]]:
    """
    Create a task execution node configured for a specific workflow.
    
    Args:
        config: Workflow configuration
        
    Returns:
        Task execution node function
    """
    
    def task_node(state: PlannedWorkflowState) -> Dict[str, Any]:
        """
        Task execution node that processes the current task in the plan.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with task execution results
        """
        start_time = time.time()
        
        try:
            # Get current plan and task
            plan = state.get("plan")
            if not plan or not plan.tasks:
                return {
                    "error": "No plan or tasks available for execution",
                    "processing_time": time.time() - start_time
                }
            
            # Get current task
            current_task_index = plan.current_task_index
            if current_task_index >= len(plan.tasks):
                return {
                    "analysis": "All tasks completed",
                    "needs_action": False,
                    "processing_time": time.time() - start_time
                }
            
            current_task = plan.tasks[current_task_index]
            
            # Skip if task is already completed
            if current_task.completed:
                # Move to next task
                plan.current_task_index += 1
                return {
                    "plan": plan,
                    "analysis": f"Task {current_task.id} already completed, moving to next task",
                    "processing_time": time.time() - start_time
                }
            
            # Initialize the OpenAI model for execution
            llm = ChatOpenAI(
                model=config.models.execution_model,
                temperature=config.models.temperature,
                max_tokens=config.models.max_tokens,
                openai_api_key=config.models.openai_api_key
            )
            
            # Build system prompt for task execution
            system_prompt = f"""You are an expert task executor for {config.workflow_name}.

Execute the current task from the plan. You have access to tools and should use them when needed.

Current Plan Summary: {plan.plan_summary}

Current Task: {current_task.id} - {current_task.description}
Expected Deliverable: {current_task.deliverable}

Available tools:
{_get_tools_description(config)}

Guidelines:
- Focus on executing the specific task
- Use available tools when appropriate
- Provide clear, actionable results
- If you need to call tools, make the appropriate tool calls
- Be concise but thorough in your analysis

Execute the task and provide results."""
            
            # Create messages including previous context
            messages = state.get("messages", [])
            messages.extend([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"Execute task: {current_task.description}")
            ])
            
            # Get response from model
            response = llm.invoke(messages)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Executed task {current_task.id} in {config.workflow_name}")
            
            # Add response to messages
            messages.append(response)
            
            return {
                "messages": messages,
                "analysis": f"Executed task {current_task.id}: {current_task.description}",
                "model_used": config.models.execution_model,
                "processing_time": processing_time,
                "needs_action": True  # May need tool calls
            }
            
        except Exception as e:
            logger.error(f"Error in task execution node: {e}")
            return {
                "error": f"Task execution failed: {str(e)}",
                "processing_time": time.time() - start_time
            }
    
    return task_node


def _get_tools_description(config: WorkflowConfig) -> str:
    """Generate description of available tools."""
    if not config.tools:
        return "No specific tools configured."
    
    descriptions = []
    for tool in config.tools:
        descriptions.append(f"- {tool.name}: {tool.description}")
    
    if config.subgraph:
        descriptions.append(f"- Subgraph: {config.description}")
    
    return "\n".join(descriptions)
