"""State management for the planned workflow package."""

from typing import TypedDict, Optional, List, Dict, Any, Annotated
from datetime import datetime
from pydantic import BaseModel, Field
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages

# Import the shared ExecutionPlan and PlanTask from agent.state
from agent.state import ExecutionPlan, PlanTask


class PlannedWorkflowState(TypedDict):
    """Generic state for planned workflows."""
    
    # Input and output
    input: str
    output: Optional[str]
    
    # Messages for LangGraph tool integration
    messages: Annotated[List[BaseMessage], add_messages]
    
    # Planning system
    plan: Optional[ExecutionPlan]
    
    # Loop control
    current_step: Optional[int]
    max_steps: Optional[int]
    needs_action: Optional[bool]
    
    # Analysis and action results
    analysis: Optional[str]
    action_results: Optional[List[Dict[str, Any]]]
    
    # Processing metadata
    timestamp: Optional[str]
    model_used: Optional[str]
    processing_time: Optional[float]
    
    # Error handling
    error: Optional[str]
    
    # Workflow-specific data (can be extended by subclasses)
    workflow_data: Optional[Dict[str, Any]]
    
    # Additional context
    metadata: Optional[Dict[str, Any]]


def create_initial_workflow_state(input_text: str, max_steps: int = 3) -> PlannedWorkflowState:
    """Create initial state for a planned workflow."""
    return PlannedWorkflowState(
        input=input_text,
        output=None,
        messages=[],
        plan=None,
        current_step=0,
        max_steps=max_steps,
        needs_action=False,
        analysis=None,
        action_results=[],
        timestamp=datetime.now().isoformat(),
        model_used=None,
        processing_time=None,
        error=None,
        workflow_data={},
        metadata={}
    )
