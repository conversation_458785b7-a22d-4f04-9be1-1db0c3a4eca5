"""Generic graph builder for planned workflows."""

import logging
from typing import Dict, Any, Optional
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolNode, tools_condition

from .state import PlannedWorkflowState, create_initial_workflow_state
from .config import WorkflowConfig
from .nodes import (
    create_planning_node,
    create_task_node,
    create_format_response_node,
    create_tool_results_node
)

logger = logging.getLogger(__name__)


def should_continue_after_planning(state: PlannedWorkflowState) -> str:
    """
    Conditional edge function after planning to determine next step.
    
    Args:
        state: Current workflow state
        
    Returns:
        Next node name
    """
    # Check for errors
    if state.get("error"):
        return "format_response"
    
    # Check if plan was created successfully
    plan = state.get("plan")
    if not plan or not plan.tasks:
        return "format_response"
    
    # Check if clarification is needed
    if plan.clarification_needed:
        return "format_response"  # Return with clarification questions
    
    # Start task execution
    return "task_node"


def should_continue_after_task(state: PlannedWorkflowState) -> str:
    """
    Conditional edge function after task processing to determine next step.
    
    Args:
        state: Current workflow state
        
    Returns:
        Next node name
    """
    # Check for errors
    if state.get("error"):
        return "format_response"
    
    # Check if we need to continue with more tasks
    needs_action = state.get("needs_action", False)
    if needs_action:
        plan = state.get("plan")
        if plan and plan.current_task_index < len(plan.tasks):
            return "task_node"  # Continue with next task
    
    # All tasks completed or no more action needed
    return "format_response"


def create_planned_workflow(config: WorkflowConfig) -> Any:
    """
    Create a planned workflow graph with the given configuration.
    
    Args:
        config: Workflow configuration
        
    Returns:
        Compiled LangGraph workflow
    """
    logger.info(f"Creating planned workflow: {config.workflow_name}")
    
    # Create the state graph
    workflow = StateGraph(PlannedWorkflowState)
    
    # Create configured nodes
    planning_node = create_planning_node(config)
    task_node = create_task_node(config)
    format_response_node = create_format_response_node(config)
    process_tool_results_node = create_tool_results_node(config)
    
    # Add nodes to the workflow
    workflow.add_node("planning", planning_node)
    workflow.add_node("task_node", task_node)
    workflow.add_node("process_tool_results", process_tool_results_node)
    workflow.add_node("format_response", format_response_node)
    
    # Add tools node if tools are configured
    if config.tools:
        workflow.add_node("tools", ToolNode(config.tools))
    
    # Set entry point to planning
    workflow.set_entry_point("planning")
    
    # Add conditional edge from planning
    workflow.add_conditional_edges(
        "planning",
        should_continue_after_planning,
        {
            "task_node": "task_node",
            "format_response": "format_response"
        }
    )
    
    # Add conditional edges from task_node
    if config.tools:
        # If tools are available, check for tool calls
        workflow.add_conditional_edges(
            "task_node",
            tools_condition,
            {
                "tools": "tools",
                "__end__": "process_tool_results"
            }
        )
        
        # Add edge from tools back to process_tool_results
        workflow.add_edge("tools", "process_tool_results")
    else:
        # No tools, go directly to process_tool_results
        workflow.add_edge("task_node", "process_tool_results")
    
    # Add conditional edge from process_tool_results
    workflow.add_conditional_edges(
        "process_tool_results",
        should_continue_after_task,
        {
            "task_node": "task_node",
            "format_response": "format_response"
        }
    )
    
    # Add edge from format_response to END
    workflow.add_edge("format_response", END)
    
    # Add memory for state persistence
    memory = MemorySaver()
    
    # Compile the graph
    app = workflow.compile(checkpointer=memory)
    
    logger.info(f"Successfully created {config.workflow_name} workflow")
    return app


def run_planned_workflow(
    config: WorkflowConfig,
    input_text: str,
    thread_id: str = "default"
) -> Dict[str, Any]:
    """
    Run a planned workflow with the given configuration and input.

    Args:
        config: Workflow configuration
        input_text: Input text to process
        thread_id: Thread ID for conversation tracking

    Returns:
        Workflow execution results
    """
    logger.info(f"Running {config.workflow_name} workflow")

    try:
        # Create the workflow graph
        app = create_planned_workflow(config)

        # Create initial state
        initial_state = create_initial_workflow_state(
            input_text,
            max_steps=config.max_steps
        )

        # Run the workflow
        result = app.invoke(
            initial_state,
            config={"configurable": {"thread_id": thread_id}}
        )

        logger.info(f"Completed {config.workflow_name} workflow")
        return result

    except Exception as e:
        logger.error(f"Error running {config.workflow_name} workflow: {e}")
        return {
            "error": f"Workflow execution failed: {str(e)}",
            "output": f"❌ **{config.workflow_name} Error**\n\n{str(e)}"
        }
